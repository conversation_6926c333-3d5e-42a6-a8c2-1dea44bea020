<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{$view_env_name}{$config.name}</title>
    <link rel="shortcut icon" href="{$storageUrl}{$config.web_favicon}"/>
    <link rel="stylesheet" href="__PUBLIC__/static/lib/layui/css/layui.css">
    <link rel="stylesheet" type="text/css" href="__PUBLIC__/static/common/css/shop-login.css"/>
    <!-- 引入Google字体和图标 -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
<!-- 背景动态效果 -->
<div class="background-effects">
    <!-- 动画粒子 -->
    <div class="particles-container">
        <div class="particle particle-1"></div>
        <div class="particle particle-2"></div>
        <div class="particle particle-3"></div>
        <div class="particle particle-4"></div>
        <div class="particle particle-5"></div>
    </div>

    <!-- 波浪效果 -->
    <div class="wave-effects">
        <div class="wave"></div>
        <div class="wave"></div>
        <div class="wave"></div>
    </div>
</div>

<!-- 登录容器 -->
<div class="login-container">
    <!-- 顶部Logo -->
    <div class="header-logo">
        <img class="logo-img" src="{$storageUrl}{$config.login_logo}" alt="Logo"/>
    </div>

    <!-- 主登录区域 -->
    <div class="login-main">
        <!-- 左侧装饰区域 -->
        <div class="login-left">
            <div class="left-content">
                <div class="welcome-text">
                    <h1>欢迎回来</h1>
                    <p>登录您的商家账户，开始高效的店铺管理体验</p>
                </div>
                <div class="feature-list">
                    <div class="feature-item">
                        <i class="fas fa-store"></i>
                        <span>店铺管理</span>
                    </div>
                    <div class="feature-item">
                        <i class="fas fa-chart-bar"></i>
                        <span>数据分析</span>
                    </div>
                    <div class="feature-item">
                        <i class="fas fa-users"></i>
                        <span>客户服务</span>
                    </div>
                </div>
                <!-- 高端装饰元素 -->
                <div class="decorative-elements">
                    <!-- 几何图形组合 -->
                    <div class="geometric-shapes">
                        <div class="shape shape-1"></div>
                        <div class="shape shape-2"></div>
                        <div class="shape shape-3"></div>
                        <div class="shape shape-4"></div>
                        <div class="shape shape-5"></div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 右侧登录表单区域 -->
        <div class="login-right">
            <div class="login-form-container">
                <div class="form-title animate-slide-up">商家管理后台</div>
                <div class="form-subtitle animate-slide-up" style="animation-delay: 0.1s;">请输入您的登录凭据</div>

                <form class="layui-form">
                    <!-- 账号输入 -->
                    <div class="form-group animate-slide-up" style="animation-delay: 0.2s;">
                        <div class="input-wrapper">
                            <div class="input-icon">
                                <img src="__PUBLIC__/static/common/image/login/login_number.png" alt="账号"/>
                            </div>
                            <input type="text"
                                   name="account"
                                   lay-verify="required"
                                   lay-vertype="tips"
                                   class="form-input"
                                   placeholder="账号"
                                   value=""/>
                        </div>
                    </div>

                    <!-- 密码输入 -->
                    <div class="form-group animate-slide-up" style="animation-delay: 0.3s;">
                        <div class="input-wrapper">
                            <div class="input-icon">
                                <img src="__PUBLIC__/static/common/image/login/login_password.png" alt="密码"/>
                            </div>
                            <input type="password"
                                   name="password"
                                   lay-verify="required"
                                   lay-vertype="tips"
                                   class="form-input"
                                   placeholder="密码"/>
                        </div>
                    </div>

                    <!-- 记住账号 -->
                    <div class="form-checkbox animate-slide-up" style="animation-delay: 0.35s;">
                        <input type="checkbox"
                               lay-skin="primary"
                               name="remember_account"
                               id="remember_account"
                               {notempty name="account"}checked=""{/notempty}>
                        <label for="remember_account">记住账号</label>
                    </div>

                    <!-- 登录按钮 -->
                    <button type="submit"
                            id="login"
                            lay-filter="login"
                            class="login-btn animate-slide-up"
                            style="animation-delay: 0.4s;"
                            lay-submit>
                        <span class="btn-text">登录</span>
                        <i class="fas fa-arrow-right btn-icon"></i>
                    </button>

                    <!-- 找回密码链接 -->
                    <div class="forgot-password animate-slide-up" style="animation-delay: 0.45s;">
                        <a href="javascript:void(0)" onclick="showForgotPassword()" class="forgot-link">
                            <i class="fas fa-key"></i>
                            忘记密码？
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- 底部版权信息 -->
    <footer class="login-footer">
        <div class="footer-content">
            <span>{$config.company_name}</span>
            <span class="separator">|</span>
            <a href="{$config.link}" target="_blank" class="footer-link">{$config.number}</a>
        </div>
    </footer>
</div>

</body>

<script src="__PUBLIC__/static/lib/layui/layui.js"></script>
<script src="__PUBLIC__/static/admin/js/jquery.min.js"></script>
<script src="__PUBLIC__/static/admin/js/function.js"></script>

<script>
    if (self != top) {
        parent.window.location.replace(window.location.href);
    }

    layui.use('form', function(){
        var form = layui.form;
        form.on('submit(login)', function (obj) {
            login(obj);
            return false; // 阻止表单默认提交
        });
    });

    function login(obj) {
        // 显示加载状态
        var loadingIndex = layer.load(2, {shade: [0.3, '#000']});

        like.ajax({
            url: '{:url("login/login")}',
            data: obj.field,
            type: 'post',
            success: function (res) {
                layer.close(loadingIndex); // 关闭加载状态

                if (res.code == 1) {
                    layer.msg(res.msg, {
                        offset: '15px',
                        icon: 1,
                        time: 1000
                    }, function () {
                        location.href = '/shop/index/index.html';
                    });
                } else {
                    layer.msg(res.msg || '登录失败', {
                        offset: '15px',
                        icon: 2,
                        time: 2000
                    });
                }
            },
            error: function(xhr, status, error) {
                layer.close(loadingIndex); // 关闭加载状态
                layer.msg('网络错误，请重试', {
                    offset: '15px',
                    icon: 2,
                    time: 2000
                });
                console.error('登录请求失败:', error);
            }
        });
    }

    // 显示找回密码弹窗
    function showForgotPassword() {
        layer.open({
            type: 1,
            title: '找回密码',
            area: ['450px', 'auto'],
            content: `
                <div style="padding: 20px;">
                    <form class="layui-form" id="forgotPasswordForm">
                        <div class="layui-form-item">
                            <label class="layui-form-label">手机号</label>
                            <div class="layui-input-block">
                                <input type="text" name="mobile" lay-verify="required|phone"
                                       placeholder="请输入注册时的手机号" class="layui-input">
                            </div>
                        </div>
                        <div class="layui-form-item">
                            <label class="layui-form-label">验证码</label>
                            <div class="layui-input-inline" style="width: 200px;">
                                <input type="text" name="code" lay-verify="required"
                                       placeholder="请输入验证码" class="layui-input">
                            </div>
                            <div class="layui-input-inline">
                                <button type="button" class="layui-btn layui-btn-primary"
                                        id="sendCodeBtn" onclick="sendResetCode()">发送验证码</button>
                            </div>
                        </div>
                        <div class="layui-form-item">
                            <label class="layui-form-label">新密码</label>
                            <div class="layui-input-block">
                                <input type="password" name="password" lay-verify="required"
                                       placeholder="请输入新密码(6-20位)" class="layui-input">
                            </div>
                        </div>
                        <div class="layui-form-item">
                            <label class="layui-form-label">确认密码</label>
                            <div class="layui-input-block">
                                <input type="password" name="confirm_password" lay-verify="required"
                                       placeholder="请再次输入新密码" class="layui-input">
                            </div>
                        </div>
                        <div class="layui-form-item">
                            <div class="layui-input-block">
                                <button type="submit" class="layui-btn" lay-submit lay-filter="resetPassword">
                                    重置密码
                                </button>
                                <button type="button" class="layui-btn layui-btn-primary"
                                        onclick="layer.closeAll()">取消</button>
                            </div>
                        </div>
                        <input type="hidden" name="token" id="resetToken">
                    </form>
                </div>
            `,
            success: function(layero, index) {
                // 重新渲染表单
                layui.form.render();

                // 绑定重置密码表单提交事件
                layui.form.on('submit(resetPassword)', function(data) {
                    resetPassword(data.field);
                    return false;
                });
            }
        });
    }

    // 发送重置密码验证码
    function sendResetCode() {
        var mobile = $('input[name="mobile"]').val();
        if (!mobile) {
            layer.msg('请输入手机号', {icon: 2});
            return;
        }

        var btn = $('#sendCodeBtn');
        btn.prop('disabled', true).text('发送中...');

        like.ajax({
            url: '{:url("passwordReset/sendCode")}',
            data: {mobile: mobile,client:5,key:'ZHMM'},
            type: 'post',
            success: function(res) {
                if (res.code == 1) {
                    layer.msg('验证码发送成功', {icon: 1});
                    // 开始倒计时
                    var countdown = 60;
                    var timer = setInterval(function() {
                        countdown--;
                        btn.text(countdown + 's后重发');
                        if (countdown <= 0) {
                            clearInterval(timer);
                            btn.prop('disabled', false).text('发送验证码');
                        }
                    }, 1000);
                } else {
                    layer.msg(res.msg || '发送失败', {icon: 2});
                    btn.prop('disabled', false).text('发送验证码');
                }
            },
            error: function() {
                layer.msg('网络错误，请重试', {icon: 2});
                btn.prop('disabled', false).text('发送验证码');
            }
        });
    }

    // 重置密码
    function resetPassword(data) {
        if (data.password !== data.confirm_password) {
            layer.msg('两次输入的密码不一致', {icon: 2});
            return;
        }

        // 先验证验证码
        like.ajax({
            url: '{:url("passwordReset/verifyCode")}',
            data: {
                mobile: data.mobile,
                code: data.code
            },
            type: 'post',
            success: function(res) {
                if (res.code == 1) {
                    // 验证码正确，执行密码重置
                    $('#resetToken').val(res.data.token);

                    like.ajax({
                        url: '{:url("passwordReset/resetPassword")}',
                        data: {
                            token: res.data.token,
                            password: data.password,
                            confirm_password: data.confirm_password
                        },
                        type: 'post',
                        success: function(resetRes) {
                            if (resetRes.code == 1) {
                                layer.msg('密码重置成功，请重新登录', {
                                    icon: 1,
                                    time: 2000
                                }, function() {
                                    layer.closeAll();
                                });
                            } else {
                                layer.msg(resetRes.msg || '重置失败', {icon: 2});
                            }
                        },
                        error: function() {
                            layer.msg('网络错误，请重试', {icon: 2});
                        }
                    });
                } else {
                    layer.msg(res.msg || '验证码错误', {icon: 2});
                }
            },
            error: function() {
                layer.msg('网络错误，请重试', {icon: 2});
            }
        });
    }

    // 回车键登录
    $(document).ready(function() {
        like.keyUpClick('[name="account"]', '#login');
        like.keyUpClick('[name="password"]', '#login');

    });

</script>
</html>
