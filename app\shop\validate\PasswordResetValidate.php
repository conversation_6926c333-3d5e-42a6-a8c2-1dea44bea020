<?php

namespace app\shop\validate;

use think\Validate;

/**
 * 密码重置验证器
 * Class PasswordResetValidate
 * @package app\shop\validate
 */
class PasswordResetValidate extends Validate
{
    protected $rule = [
        'mobile' => 'require|mobile',
        'code' => 'require|length:6',
        'token' => 'require',
        'password' => 'require|min:6|max:20',
        'confirm_password' => 'require|confirm:password'
    ];

    protected $message = [
        'mobile.require' => '手机号不能为空',
        'mobile.mobile' => '手机号格式不正确',
        'code.require' => '验证码不能为空',
        'code.length' => '验证码必须为6位数字',
        'token.require' => '重置令牌不能为空',
        'password.require' => '密码不能为空',
        'password.min' => '密码长度不能少于6位',
        'password.max' => '密码长度不能超过20位',
        'confirm_password.require' => '确认密码不能为空',
        'confirm_password.confirm' => '两次输入的密码不一致'
    ];

    protected $scene = [
        'sendCode' => ['mobile'],
        'verifyCode' => ['mobile', 'code'],
        'resetPassword' => ['token', 'password', 'confirm_password']
    ];
}
