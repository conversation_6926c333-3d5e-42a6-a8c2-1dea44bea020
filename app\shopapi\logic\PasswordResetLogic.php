<?php

namespace app\shopapi\logic;

use app\common\basics\Logic;
use app\common\model\shop\ShopAdmin;
use app\common\model\Shop;
use app\common\logic\SmsLogic;
use think\facade\Db;
use think\facade\Cache;

/**
 * 商家API密码重置逻辑
 * Class PasswordResetLogic
 * @package app\shopapi\logic
 */
class PasswordResetLogic extends Logic
{
    /**
     * 验证手机号是否存在对应的商家账号
     * @param string $mobile
     * @return array|string 成功返回管理员信息，失败返回错误信息
     */
    public static function checkMobileExists($mobile)
    {
        try {
            // 方式1：通过shop表的mobile字段查找
            $shop = Shop::where(['mobile' => $mobile, 'del' => 0])->findOrEmpty();
            if (!$shop->isEmpty()) {
                // 查找对应的管理员账号
                $admin = ShopAdmin::where(['shop_id' => $shop['id'], 'del' => 0, 'root' => 1])->findOrEmpty();
                if (!$admin->isEmpty()) {
                    return [
                        'admin_id' => $admin['id'],
                        'shop_id' => $shop['id'],
                        'account' => $admin['account'],
                        'mobile' => $mobile
                    ];
                }
            }
            
            // 方式2：直接通过管理员账号查找（如果账号就是手机号）
            $admin = ShopAdmin::alias('a')
                ->join('shop s', 's.id = a.shop_id')
                ->where(['a.account' => $mobile, 'a.del' => 0, 's.del' => 0])
                ->field('a.id as admin_id, a.shop_id, a.account, s.mobile')
                ->findOrEmpty();
                
            if (!$admin->isEmpty()) {
                return [
                    'admin_id' => $admin['admin_id'],
                    'shop_id' => $admin['shop_id'],
                    'account' => $admin['account'],
                    'mobile' => $mobile
                ];
            }
            
            return '该手机号未注册商家账号';
            
        } catch (\Exception $e) {
            return '查询账号失败：' . $e->getMessage();
        }
    }

    /**
     * 验证重置码
     * @param array $params
     * @return array|string
     */
    public static function verifyResetCode($params)
    {
        try {
            $mobile = $params['mobile'];
            $code = $params['code'];
            
            // 先验证手机号是否存在对应的商家账号
            $accountInfo = self::checkMobileExists($mobile);
            if (is_string($accountInfo)) {
                return $accountInfo;
            }
            
            // 查找有效的验证码记录
            $resetRecord = Db::name('sms_log')
                ->where([
                    'mobile' => $mobile,
                    'code' => $code,
                    'is_verify' => 0
                ])
                ->where('send_time', '>', time() - 300) // 5分钟有效期
                ->order('send_time desc')
                ->find();
                
            if (!$resetRecord) {
                return '验证码无效或已过期';
            }
            
            // 生成重置令牌
            $token = md5($mobile . $code . time() . mt_rand(1000, 9999));
            
            // 将令牌保存到缓存中，用于后续密码重置验证
            Cache::set('password_reset_token_' . $token, [
                'mobile' => $mobile,
                'admin_id' => $accountInfo['admin_id'],
                'account' => $accountInfo['account'],
                'sms_log_id' => $resetRecord['id']
            ], 600); // 10分钟有效期
            
            return [
                'token' => $token,
                'admin_id' => $accountInfo['admin_id']
            ];
            
        } catch (\Exception $e) {
            return '验证失败：' . $e->getMessage();
        }
    }

    /**
     * 重置密码
     * @param array $params
     * @return bool|string
     */
    public static function resetPassword($params)
    {
        try {
            $token = $params['token'];
            $newPassword = $params['password'];
            
            // 从缓存中获取重置信息
            $resetInfo = Cache::get('password_reset_token_' . $token);
            if (!$resetInfo) {
                return '重置令牌无效或已过期';
            }
            
            // 生成新的密码盐和加密密码
            $salt = create_salt();
            $password = create_password($newPassword, $salt);
            
            // 更新管理员密码
            $updateResult = ShopAdmin::where(['id' => $resetInfo['admin_id']])
                ->update([
                    'password' => $password,
                    'salt' => $salt,
                    'update_time' => time()
                ]);
                
            if (!$updateResult) {
                return '密码更新失败';
            }
            
            // 标记验证码记录为已使用
            Db::name('sms_log')
                ->where(['id' => $resetInfo['sms_log_id']])
                ->update([
                    'is_verify' => 1,
                    'update_time' => time()
                ]);
            
            // 清除缓存中的令牌
            Cache::delete('password_reset_token_' . $token);
            
            return true;
            
        } catch (\Exception $e) {
            return '重置密码失败：' . $e->getMessage();
        }
    }
    
    /**
     * 发送重置密码验证码
     * @param string $mobile 手机号
     * @param string $key 短信模板key
     * @param int $user_id 用户ID（可选）
     * @return array|string 成功返回结果数组，失败返回错误信息
     */
    public static function sendCode($mobile, $key = 'SJZHMM', $user_id = 0)
    {
        try {
            // 验证手机号是否存在对应的商家账号
            $accountInfo = self::checkMobileExists($mobile);
            if (is_string($accountInfo)) {
                return $accountInfo;
            }

            // 检查发送频率限制（1分钟内只能发送一次）
            $cacheKey = 'password_reset_' . $mobile;
            if (Cache::get($cacheKey)) {
                return '验证码发送过于频繁，请稍后再试';
            }

            // 发送短信验证码 - 使用正确的参数顺序
            $result = SmsLogic::send($mobile, $key, $user_id);

            if (is_string($result)) {
                return $result; // 发送失败，返回错误信息
            }

            // 设置缓存，防止频繁发送
            Cache::set($cacheKey, true, 60);

            return $result;

        } catch (\Exception $e) {
            return '发送验证码失败：' . $e->getMessage();
        }
    }
}
